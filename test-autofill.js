// location/test-autofill.js
// Simple test script to verify autofill functionality works
// Tests both the debugging improvements and basic autofill flow

async function testAutofillFunctionality() {
  console.log('🧪 Starting Autofill Functionality Test...')
  
  try {
    // Test 1: Check if extension is loaded
    console.log('\n📋 Test 1: Extension Status')
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      console.log('✅ Chrome extension API available')
    } else {
      console.log('❌ Chrome extension API not available')
      return
    }
    
    // Test 2: Check content script injection
    console.log('\n📋 Test 2: Content Script Communication')
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab) {
        console.log('❌ No active tab found')
        return
      }
      
      console.log(`📄 Active tab: ${tab.url}`)
      
      // Try to ping content script
      const pingResponse = await chrome.tabs.sendMessage(tab.id, { action: 'ping' })
      if (pingResponse && pingResponse.success) {
        console.log('✅ Content script is active and responding')
      } else {
        console.log('❌ Content script not responding')
      }
    } catch (error) {
      console.log(`❌ Content script communication failed: ${error.message}`)
    }
    
    // Test 3: Check settings
    console.log('\n📋 Test 3: Settings Configuration')
    try {
      const settingsResponse = await chrome.runtime.sendMessage({ action: 'getSettings' })
      if (settingsResponse && settingsResponse.success) {
        const settings = settingsResponse.data
        console.log('✅ Settings loaded successfully')
        console.log(`🔑 API Key configured: ${settings.apiKey ? 'Yes' : 'No'}`)
        console.log(`⚙️ Auto-analyze: ${settings.uiConfig?.autoAnalyze ? 'Enabled' : 'Disabled'}`)
      } else {
        console.log('❌ Failed to load settings')
      }
    } catch (error) {
      console.log(`❌ Settings test failed: ${error.message}`)
    }
    
    // Test 4: Check profiles
    console.log('\n📋 Test 4: Profile Management')
    try {
      // This would need to be run in the extension context
      console.log('ℹ️ Profile test requires extension context')
    } catch (error) {
      console.log(`❌ Profile test failed: ${error.message}`)
    }
    
    // Test 5: Page analysis
    console.log('\n📋 Test 5: Page Analysis')
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tab && tab.url.startsWith('http')) {
        const analysisResponse = await chrome.tabs.sendMessage(tab.id, { action: 'analyzePage' })
        if (analysisResponse && analysisResponse.success) {
          console.log('✅ Page analysis successful')
          console.log(`📊 Forms found: ${analysisResponse.formCount}`)
          console.log(`📝 Fields found: ${analysisResponse.fieldCount}`)
        } else {
          console.log(`❌ Page analysis failed: ${analysisResponse?.error || 'Unknown error'}`)
        }
      } else {
        console.log('ℹ️ Page analysis requires a regular webpage (not chrome:// or extension pages)')
      }
    } catch (error) {
      console.log(`❌ Page analysis test failed: ${error.message}`)
    }
    
    console.log('\n🎉 Test completed!')
    
  } catch (error) {
    console.error('❌ Test suite failed:', error)
  }
}

// Export for use in extension context
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testAutofillFunctionality }
}

// Auto-run if in browser context
if (typeof window !== 'undefined') {
  // Run test when page loads
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testAutofillFunctionality)
  } else {
    testAutofillFunctionality()
  }
}
