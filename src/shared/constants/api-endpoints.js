// src/shared/constants/api-endpoints.js
// API endpoint constants for Gemini AI model integration
// Defines base URLs, model endpoints, and configuration parameters

window.API_ENDPOINTS = {
  GEMINI_BASE_URL: 'https://generativelanguage.googleapis.com/v1beta',
  GEMINI_MODEL: 'models/gemini-2.5-flash-lite-preview-06-17',
  GENERATE_CONTENT: '/generateContent'
}

window.API_CONFIG = {
  DEFAULT_TEMPERATURE: 0.1,
  DEFAULT_MAX_OUTPUT_TOKENS: 1000,
  DEFAULT_THINKING_BUDGET: 0,
  REQUEST_TIMEOUT: 30000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000
}

window.HEADERS = {
  'Content-Type': 'application/json'
}
