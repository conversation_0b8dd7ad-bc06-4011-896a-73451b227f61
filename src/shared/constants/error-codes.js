// src/shared/constants/error-codes.js
// Error code definitions for consistent error handling
// Defines error types, codes, and user-friendly messages

export const ERROR_CODES = {
  // API Errors
  API_KEY_MISSING: 'API_KEY_MISSING',
  API_REQUEST_FAILED: 'API_REQUEST_FAILED',
  API_RATE_LIMITED: 'API_RATE_LIMITED',
  API_INVALID_RESPONSE: 'API_INVALID_RESPONSE',

  // Storage Errors
  STORAGE_READ_FAILED: 'STORAGE_READ_FAILED',
  STORAGE_WRITE_FAILED: 'STORAGE_WRITE_FAILED',
  STORAGE_QUOTA_EXCEEDED: 'STORAGE_QUOTA_EXCEEDED',

  // Form Processing Errors
  NO_FORMS_FOUND: 'NO_FORMS_FOUND',
  FORM_ANALYSIS_FAILED: 'FORM_ANALYSIS_FAILED',
  FIELD_INJECTION_FAILED: 'FIELD_INJECTION_FAILED',

  // Profile Errors
  PROFILE_NOT_FOUND: 'PROFILE_NOT_FOUND',
  PROFILE_VALIDATION_FAILED: 'PROFILE_VALIDATION_FAILED',
  PROFILE_SAVE_FAILED: 'PROFILE_SAVE_FAILED'
}

export const ERROR_MESSAGES = {
  [ERROR_CODES.API_KEY_MISSING]: 'API key is required. Please configure your Gemini API key in settings.',
  [ERROR_CODES.API_REQUEST_FAILED]: 'Failed to connect to AI service. Please check your internet connection.',
  [ERROR_CODES.API_RATE_LIMITED]: 'API rate limit exceeded. Please try again later.',
  [ERROR_CODES.API_INVALID_RESPONSE]: 'Received invalid response from AI service.',

  [ERROR_CODES.STORAGE_READ_FAILED]: 'Failed to read data from storage.',
  [ERROR_CODES.STORAGE_WRITE_FAILED]: 'Failed to save data to storage.',
  [ERROR_CODES.STORAGE_QUOTA_EXCEEDED]: 'Storage quota exceeded. Please remove some profiles.',

  [ERROR_CODES.NO_FORMS_FOUND]: 'No forms found on this page.',
  [ERROR_CODES.FORM_ANALYSIS_FAILED]: 'Failed to analyze form structure.',
  [ERROR_CODES.FIELD_INJECTION_FAILED]: 'Failed to fill form fields.',

  [ERROR_CODES.PROFILE_NOT_FOUND]: 'Profile not found.',
  [ERROR_CODES.PROFILE_VALIDATION_FAILED]: 'Profile data is invalid.',
  [ERROR_CODES.PROFILE_SAVE_FAILED]: 'Failed to save profile.'
}
