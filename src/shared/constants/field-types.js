// src/shared/constants/field-types.js
// Form field type definitions and semantic labels
// Defines supported input types and their semantic mappings

window.FIELD_TYPES = {
  TEXT: 'text',
  EMAIL: 'email',
  PASSWORD: 'password',
  TEL: 'tel',
  URL: 'url',
  NUMBER: 'number',
  DATE: 'date',
  TEXTAREA: 'textarea',
  SELECT: 'select',
  CHECKBOX: 'checkbox',
  RADIO: 'radio'
}

window.SEMANTIC_LABELS = {
  // Personal Information
  FIRST_NAME: 'first_name',
  LAST_NAME: 'last_name',
  FULL_NAME: 'full_name',
  EMAIL: 'email',
  PHONE: 'phone',
  DATE_OF_BIRTH: 'date_of_birth',

  // Address Information
  ADDRESS_LINE_1: 'address_line_1',
  ADDRESS_LINE_2: 'address_line_2',
  CITY: 'city',
  STATE: 'state',
  POSTAL_CODE: 'postal_code',
  COUNTRY: 'country',

  // Other Common Fields
  COMPANY: 'company',
  JOB_TITLE: 'job_title',
  WEBSITE: 'website',
  USERNAME: 'username',
  PASSWORD: 'password'
}

window.FIELD_SELECTORS = [
  'input[type="text"]',
  'input[type="email"]',
  'input[type="tel"]',
  'input[type="url"]',
  'input[type="number"]',
  'input[type="date"]',
  'input[type="password"]',
  'textarea',
  'select'
]
