// src/shared/utils/format-utils.js
// Data formatting and transformation utility functions
// Provides methods for formatting dates, text, and data structures

export class FormatUtils {
  static formatDate (date, options = {}) {
    const defaultOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }

    const formatOptions = { ...defaultOptions, ...options }

    try {
      const dateObj = new Date(date)
      return dateObj.toLocaleDateString('en-US', formatOptions)
    } catch {
      return 'Invalid Date'
    }
  }

  static formatRelativeTime (date) {
    try {
      const now = new Date()
      const target = new Date(date)
      const diffMs = now - target
      const diffMins = Math.floor(diffMs / 60000)
      const diffHours = Math.floor(diffMs / 3600000)
      const diffDays = Math.floor(diffMs / 86400000)

      if (diffMins < 1) return 'Just now'
      if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`
      if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
      if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`

      return this.formatDate(date, { year: 'numeric', month: 'short', day: 'numeric' })
    } catch {
      return 'Unknown'
    }
  }

  static truncateText (text, maxLength = 100, suffix = '...') {
    if (!text || typeof text !== 'string') return ''

    if (text.length <= maxLength) return text

    return text.substring(0, maxLength - suffix.length) + suffix
  }

  static capitalizeFirst (text) {
    if (!text || typeof text !== 'string') return ''

    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
  }

  static camelToKebab (text) {
    return text.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
  }

  static kebabToCamel (text) {
    return text.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase())
  }

  static formatFileSize (bytes) {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }

  static formatNumber (number, options = {}) {
    const defaultOptions = {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }

    const formatOptions = { ...defaultOptions, ...options }

    try {
      return new Intl.NumberFormat('en-US', formatOptions).format(number)
    } catch {
      return String(number)
    }
  }

  static formatPercentage (value, total) {
    if (total === 0) return '0%'

    const percentage = (value / total) * 100
    return `${Math.round(percentage)}%`
  }

  static escapeHtml (text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  static unescapeHtml (html) {
    const div = document.createElement('div')
    div.innerHTML = html
    return div.textContent || div.innerText || ''
  }

  static generateSlug (text) {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
  }

  static parseJSON (jsonString, defaultValue = null) {
    try {
      return JSON.parse(jsonString)
    } catch {
      return defaultValue
    }
  }

  static stringifyJSON (object, pretty = false) {
    try {
      return JSON.stringify(object, null, pretty ? 2 : 0)
    } catch {
      return '{}'
    }
  }
}
