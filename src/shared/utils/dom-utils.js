// src/shared/utils/dom-utils.js
// DOM manipulation and analysis utility functions
// Provides form detection, field analysis, and element interaction methods

window.DOMUtils = class DOMUtils {
  static findForms () {
    return Array.from(document.querySelectorAll('form'))
  }

  static findFormFields (form = document) {
    const fields = []

    window.FIELD_SELECTORS.forEach(selector => {
      const elements = form.querySelectorAll(selector)
      elements.forEach(element => {
        if (this.isValidFormField(element)) {
          fields.push(element)
        }
      })
    })

    return fields
  }

  static isValidFormField (element) {
    // Skip hidden fields, disabled fields, and readonly fields
    if (element.type === 'hidden' ||
        element.disabled ||
        element.readOnly ||
        element.style.display === 'none' ||
        element.style.visibility === 'hidden') {
      return false
    }

    // Skip fields that are not visible
    const rect = element.getBoundingClientRect()
    if (rect.width === 0 || rect.height === 0) {
      return false
    }

    return true
  }

  static getFieldType (element) {
    if (element.tagName.toLowerCase() === 'textarea') {
      return window.FIELD_TYPES.TEXTAREA
    }

    if (element.tagName.toLowerCase() === 'select') {
      return window.FIELD_TYPES.SELECT
    }

    return element.type || window.FIELD_TYPES.TEXT
  }

  static getFieldLabel (element) {
    // Try to find associated label
    if (element.id) {
      const label = document.querySelector(`label[for="${element.id}"]`)
      if (label) {
        return label.textContent.trim()
      }
    }

    // Try to find parent label
    const parentLabel = element.closest('label')
    if (parentLabel) {
      return parentLabel.textContent.replace(element.value || '', '').trim()
    }

    // Try to find nearby text
    const previousSibling = element.previousElementSibling
    if (previousSibling && previousSibling.textContent) {
      return previousSibling.textContent.trim()
    }

    return ''
  }

  static getFieldSelector (element) {
    // Prefer ID selector
    if (element.id) {
      return `#${element.id}`
    }

    // Use name attribute
    if (element.name) {
      return `${element.tagName.toLowerCase()}[name="${element.name}"]`
    }

    // Generate CSS selector based on position
    const parent = element.parentElement
    if (parent) {
      const siblings = Array.from(parent.children)
      const index = siblings.indexOf(element)
      return `${parent.tagName.toLowerCase()} > ${element.tagName.toLowerCase()}:nth-child(${index + 1})`
    }

    return element.tagName.toLowerCase()
  }

  static fillField (element, value) {
    if (!element || !value) return false

    try {
      // Set the value
      element.value = value

      // Trigger events to ensure the change is detected
      this.triggerEvents(element)

      return true
    } catch (error) {
      console.error('Error filling field:', error)
      return false
    }
  }

  static triggerEvents (element) {
    const events = ['input', 'change', 'blur']

    events.forEach(eventType => {
      const event = new Event(eventType, { bubbles: true })
      element.dispatchEvent(event)
    })
  }

  static highlightField (element, duration = 2000) {
    const originalStyle = element.style.cssText

    element.style.cssText += `
      border: 2px solid #667eea !important;
      box-shadow: 0 0 10px rgba(102, 126, 234, 0.3) !important;
      transition: all 0.3s ease !important;
    `

    setTimeout(() => {
      element.style.cssText = originalStyle
    }, duration)
  }
}
