// src/shared/utils/storage-utils.js
// Browser storage utility functions for data persistence
// Provides localStorage and chrome.storage API wrappers with error handling

import { ERROR_CODES, ERROR_MESSAGES } from '../constants/error-codes.js'

export class StorageUtils {
  static async get (key, defaultValue = null) {
    try {
      // Try chrome.storage.local first (for extension context)
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(key)
        return result[key] !== undefined ? result[key] : defaultValue
      }

      // Fallback to localStorage
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error('Storage read error:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_READ_FAILED])
    }
  }

  static async set (key, value) {
    try {
      // Try chrome.storage.local first (for extension context)
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ [key]: value })
        return
      }

      // Fallback to localStorage
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Storage write error:', error)
      if (error.name === 'QuotaExceededError') {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_QUOTA_EXCEEDED])
      }
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_WRITE_FAILED])
    }
  }

  static async remove (key) {
    try {
      // Try chrome.storage.local first (for extension context)
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.remove(key)
        return
      }

      // Fallback to localStorage
      localStorage.removeItem(key)
    } catch (error) {
      console.error('Storage remove error:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_WRITE_FAILED])
    }
  }

  static async clear () {
    try {
      // Try chrome.storage.local first (for extension context)
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.clear()
        return
      }

      // Fallback to localStorage
      localStorage.clear()
    } catch (error) {
      console.error('Storage clear error:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_WRITE_FAILED])
    }
  }

  static async getAll () {
    try {
      // Try chrome.storage.local first (for extension context)
      if (typeof chrome !== 'undefined' && chrome.storage) {
        return await chrome.storage.local.get()
      }

      // Fallback to localStorage
      const result = {}
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        result[key] = JSON.parse(localStorage.getItem(key))
      }
      return result
    } catch (error) {
      console.error('Storage getAll error:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_READ_FAILED])
    }
  }
}
