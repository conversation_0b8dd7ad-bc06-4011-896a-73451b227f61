// src/shared/services/autofill-service.js
// Autofill engine service for form analysis and field injection
// Coordinates between DOM analysis, AI processing, and field filling

window.AutofillService = class AutofillService {
  constructor (apiKey) {
    this.aiService = new window.AIService(apiKey)
  }

  async analyzeCurrentPage () {
    try {
      const forms = window.DOMUtils.findForms()

      if (forms.length === 0) {
        throw new Error(window.ERROR_MESSAGES[window.ERROR_CODES.NO_FORMS_FOUND])
      }

      // Analyze the first form (or most relevant form)
      const form = this.selectBestForm(forms)
      const formModel = await this.analyzeForm(form)

      return formModel
    } catch (error) {
      console.error('Page analysis failed:', error)
      throw error
    }
  }

  selectBestForm (forms) {
    // Prioritize forms with more fields
    return forms.reduce((best, current) => {
      const bestFields = window.DOMUtils.findFormFields(best).length
      const currentFields = window.DOMUtils.findFormFields(current).length
      return currentFields > bestFields ? current : best
    })
  }

  async analyzeForm (formElement) {
    try {
      const formModel = new window.FormModel({
        formSelector: window.DOMUtils.getFieldSelector(formElement)
      })

      const fields = window.DOMUtils.findFormFields(formElement)

      for (const fieldElement of fields) {
        const fieldModel = new window.FormFieldModel({
          selector: window.DOMUtils.getFieldSelector(fieldElement),
          type: window.DOMUtils.getFieldType(fieldElement),
          label: window.DOMUtils.getFieldLabel(fieldElement),
          placeholder: fieldElement.placeholder || '',
          name: fieldElement.name || '',
          id: fieldElement.id || '',
          required: fieldElement.required || false,
          element: fieldElement
        })

        formModel.addField(fieldModel)
      }

      return formModel
    } catch (error) {
      console.error('Form analysis failed:', error)
      throw new Error(window.ERROR_MESSAGES[window.ERROR_CODES.FORM_ANALYSIS_FAILED])
    }
  }

  async fillForm (formModel, profileInfo) {
    try {
      // Get AI analysis for field mappings
      const aiResponse = await this.aiService.analyzeForm(
        formModel.toAIPrompt(),
        profileInfo
      )

      const results = {
        filled: [],
        failed: [],
        skipped: []
      }

      // Process field mappings
      for (const mapping of aiResponse.fieldMappings) {
        try {
          const field = formModel.getFieldBySelector(mapping.selector)

          if (!field || !field.element) {
            results.skipped.push({
              selector: mapping.selector,
              reason: 'Field element not found'
            })
            continue
          }

          // Only fill if confidence is high enough
          if (mapping.confidence < 0.7) {
            results.skipped.push({
              selector: mapping.selector,
              reason: `Low confidence: ${mapping.confidence}`
            })
            continue
          }

          const success = window.DOMUtils.fillField(field.element, mapping.value)

          if (success) {
            window.DOMUtils.highlightField(field.element)
            results.filled.push({
              selector: mapping.selector,
              value: mapping.value,
              confidence: mapping.confidence
            })
          } else {
            results.failed.push({
              selector: mapping.selector,
              reason: 'Failed to set field value'
            })
          }
        } catch (error) {
          console.error('Error filling field:', error)
          results.failed.push({
            selector: mapping.selector,
            reason: error.message
          })
        }
      }

      // Add unmapped fields to skipped
      if (aiResponse.unmappedFields) {
        results.skipped.push(...aiResponse.unmappedFields)
      }

      return results
    } catch (error) {
      console.error('Form filling failed:', error)
      throw new Error(window.ERROR_MESSAGES[window.ERROR_CODES.FIELD_INJECTION_FAILED])
    }
  }

  async performAutofill (profileInfo) {
    try {
      // Step 1: Analyze current page
      const formModel = await this.analyzeCurrentPage()

      // Step 2: Fill the form
      const results = await this.fillForm(formModel, profileInfo)

      return {
        success: true,
        formModel,
        results
      }
    } catch (error) {
      console.error('Autofill failed:', error)
      return {
        success: false,
        error: error.message,
        formModel: null,
        results: null
      }
    }
  }

  async previewAutofill (profileInfo) {
    try {
      // Analyze form without actually filling it
      const formModel = await this.analyzeCurrentPage()

      // Get AI predictions
      const aiResponse = await this.aiService.analyzeForm(
        formModel.toAIPrompt(),
        profileInfo
      )

      return {
        success: true,
        formModel,
        predictions: aiResponse.fieldMappings,
        unmapped: aiResponse.unmappedFields || []
      }
    } catch (error) {
      console.error('Autofill preview failed:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  validateFormStructure (formModel) {
    const issues = []

    if (!formModel.fields || formModel.fields.length === 0) {
      issues.push('No form fields detected')
    }

    const duplicateSelectors = new Set()
    const selectors = formModel.fields.map(f => f.selector)

    for (const selector of selectors) {
      if (selectors.filter(s => s === selector).length > 1) {
        duplicateSelectors.add(selector)
      }
    }

    if (duplicateSelectors.size > 0) {
      issues.push(`Duplicate field selectors: ${Array.from(duplicateSelectors).join(', ')}`)
    }

    return {
      isValid: issues.length === 0,
      issues
    }
  }
}
