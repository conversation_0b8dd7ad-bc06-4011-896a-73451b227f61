// src/shared/services/profile-service.js
// User profile management service
// Handles CRUD operations for user profiles with localStorage persistence

import { ProfileModel } from '../models/profile-model.js'
import { StorageUtils } from '../utils/storage-utils.js'
import { ERROR_CODES, ERROR_MESSAGES } from '../constants/error-codes.js'

const STORAGE_KEY = 'autofill_profiles'

export class ProfileService {
  static async getAllProfiles () {
    try {
      const profiles = await StorageUtils.get(STORAGE_KEY, [])
      return profiles.map(profileData => ProfileModel.fromJSON(profileData))
    } catch (error) {
      console.error('Failed to load profiles:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_READ_FAILED])
    }
  }

  static async getProfile (id) {
    try {
      const profiles = await this.getAllProfiles()
      const profile = profiles.find(p => p.id === id)

      if (!profile) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.PROFILE_NOT_FOUND])
      }

      return profile
    } catch (error) {
      if (error.message.includes('PROFILE_NOT_FOUND')) {
        throw error
      }
      console.error('Failed to get profile:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_READ_FAILED])
    }
  }

  static async saveProfile (profileData) {
    try {
      // Create or update profile
      const profile = profileData instanceof ProfileModel
        ? profileData
        : new ProfileModel(profileData)

      // Validate profile
      const validation = profile.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      // Get existing profiles
      const profiles = await this.getAllProfiles()

      // Check for existing profile with same ID
      const existingIndex = profiles.findIndex(p => p.id === profile.id)

      if (existingIndex >= 0) {
        // Update existing profile
        profiles[existingIndex] = profile
      } else {
        // Add new profile
        profiles.push(profile)
      }

      // Save to storage
      await StorageUtils.set(STORAGE_KEY, profiles.map(p => p.toJSON()))

      return profile
    } catch (error) {
      console.error('Failed to save profile:', error)
      if (error.message.includes('validation') || error.message.includes('required')) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.PROFILE_VALIDATION_FAILED])
      }
      throw new Error(ERROR_MESSAGES[ERROR_CODES.PROFILE_SAVE_FAILED])
    }
  }

  static async deleteProfile (id) {
    try {
      const profiles = await this.getAllProfiles()
      const filteredProfiles = profiles.filter(p => p.id !== id)

      if (profiles.length === filteredProfiles.length) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.PROFILE_NOT_FOUND])
      }

      await StorageUtils.set(STORAGE_KEY, filteredProfiles.map(p => p.toJSON()))

      return true
    } catch (error) {
      if (error.message.includes('PROFILE_NOT_FOUND')) {
        throw error
      }
      console.error('Failed to delete profile:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_WRITE_FAILED])
    }
  }

  static async createProfile (name, info) {
    try {
      const profile = new ProfileModel({ name, info })
      return await this.saveProfile(profile)
    } catch (error) {
      console.error('Failed to create profile:', error)
      throw error
    }
  }

  static async updateProfile (id, updates) {
    try {
      const profile = await this.getProfile(id)
      profile.update(updates)
      return await this.saveProfile(profile)
    } catch (error) {
      console.error('Failed to update profile:', error)
      throw error
    }
  }

  static async duplicateProfile (id, newName) {
    try {
      const originalProfile = await this.getProfile(id)
      const duplicatedProfile = new ProfileModel({
        name: newName || `${originalProfile.name} (Copy)`,
        info: originalProfile.info
      })

      return await this.saveProfile(duplicatedProfile)
    } catch (error) {
      console.error('Failed to duplicate profile:', error)
      throw error
    }
  }

  static async searchProfiles (query) {
    try {
      const profiles = await this.getAllProfiles()
      const lowercaseQuery = query.toLowerCase()

      return profiles.filter(profile =>
        profile.name.toLowerCase().includes(lowercaseQuery) ||
        profile.info.toLowerCase().includes(lowercaseQuery)
      )
    } catch (error) {
      console.error('Failed to search profiles:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_READ_FAILED])
    }
  }

  static async getProfileCount () {
    try {
      const profiles = await this.getAllProfiles()
      return profiles.length
    } catch (error) {
      console.error('Failed to get profile count:', error)
      return 0
    }
  }

  static async exportProfiles () {
    try {
      const profiles = await this.getAllProfiles()
      return {
        version: '1.0.0',
        exportDate: new Date().toISOString(),
        profiles: profiles.map(p => p.toJSON())
      }
    } catch (error) {
      console.error('Failed to export profiles:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_READ_FAILED])
    }
  }

  static async importProfiles (importData) {
    try {
      if (!importData.profiles || !Array.isArray(importData.profiles)) {
        throw new Error('Invalid import data format')
      }

      const existingProfiles = await this.getAllProfiles()
      const importedProfiles = []

      for (const profileData of importData.profiles) {
        try {
          const profile = new ProfileModel(profileData)
          const validation = profile.validate()

          if (validation.isValid) {
            // Generate new ID to avoid conflicts
            profile.id = profile.generateId()
            importedProfiles.push(profile)
          }
        } catch (error) {
          console.warn('Skipped invalid profile during import:', error)
        }
      }

      const allProfiles = [...existingProfiles, ...importedProfiles]
      await StorageUtils.set(STORAGE_KEY, allProfiles.map(p => p.toJSON()))

      return importedProfiles.length
    } catch (error) {
      console.error('Failed to import profiles:', error)
      throw new Error(ERROR_MESSAGES[ERROR_CODES.STORAGE_WRITE_FAILED])
    }
  }
}
