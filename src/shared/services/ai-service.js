// src/shared/services/ai-service.js
// AI model service for Gemini API integration
// Handles HTTP API calls, prompt construction, and response processing

window.AIService = class AIService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = window.API_ENDPOINTS.GEMINI_BASE_URL;
    this.model = window.API_ENDPOINTS.GEMINI_MODEL;
  }

  async analyzeForm(formData, profileInfo) {
    const prompt = this.constructFormAnalysisPrompt(formData, profileInfo);

    try {
      const response = await this.makeRequest(prompt, {
        temperature: window.API_CONFIG.DEFAULT_TEMPERATURE,
        maxOutputTokens: window.API_CONFIG.DEFAULT_MAX_OUTPUT_TOKENS,
        thinkingBudget: window.API_CONFIG.DEFAULT_THINKING_BUDGET
      });

      return this.parseFormAnalysisResponse(response);
    } catch (error) {
      console.error('Form analysis failed:', error);
      throw error;
    }
  }

  constructFormAnalysisPrompt(formData, profileInfo) {
    return `You are an intelligent form filling assistant. Analyze the following form structure and user profile information, then provide field mappings.

FORM STRUCTURE:
${JSON.stringify(formData, null, 2)}

USER PROFILE:
${profileInfo}

TASK:
1. Analyze each form field and determine what information should be filled based on the user profile
2. Extract relevant information from the user profile for each field
3. Return a JSON object with field mappings

RESPONSE FORMAT:
{
  "fieldMappings": [
    {
      "selector": "field_selector",
      "value": "extracted_value",
      "confidence": 0.95,
      "reasoning": "why this value was chosen"
    }
  ],
  "unmappedFields": [
    {
      "selector": "field_selector",
      "reason": "why this field couldn't be mapped"
    }
  ]
}

GUIDELINES:
- Only map fields where you have high confidence (>0.7)
- Extract information naturally from the user profile text
- Consider field labels, placeholders, and names for context
- Be conservative - don't guess if information isn't clearly available
- Provide clear reasoning for each mapping`;
  }

  async makeRequest(prompt, config = {}) {
    if (!this.apiKey) {
      throw new Error(window.ERROR_MESSAGES[window.ERROR_CODES.API_KEY_MISSING]);
    }

    const requestConfig = {
      temperature: config.temperature ?? window.API_CONFIG.DEFAULT_TEMPERATURE,
      maxOutputTokens: config.maxOutputTokens ?? window.API_CONFIG.DEFAULT_MAX_OUTPUT_TOKENS,
      thinkingBudget: config.thinkingBudget ?? window.API_CONFIG.DEFAULT_THINKING_BUDGET
    };

    const requestBody = {
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        temperature: requestConfig.temperature,
        maxOutputTokens: requestConfig.maxOutputTokens
      }
    };

    // Add thinking config if specified
    if (requestConfig.thinkingBudget !== undefined) {
      requestBody.config = {
        thinkingConfig: {
          thinkingBudget: requestConfig.thinkingBudget
        }
      };
    }

    const url = `${this.baseUrl}/${this.model}:generateContent`;

    try {
      const response = await this.fetchWithRetry(url, {
        method: 'POST',
        headers: {
          ...window.HEADERS,
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        if (response.status === 429) {
          throw new Error(window.ERROR_MESSAGES[window.ERROR_CODES.API_RATE_LIMITED]);
        }
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error(window.ERROR_MESSAGES[window.ERROR_CODES.API_INVALID_RESPONSE]);
      }

      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      if (error.message.includes('API_')) {
        throw error;
      }
      throw new Error(window.ERROR_MESSAGES[window.ERROR_CODES.API_REQUEST_FAILED]);
    }
  }

  async fetchWithRetry(url, options, retries = window.API_CONFIG.MAX_RETRIES) {
    for (let i = 0; i <= retries; i++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), window.API_CONFIG.REQUEST_TIMEOUT);

        const response = await fetch(url, {
          ...options,
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        return response;
      } catch (error) {
        if (i === retries) throw error;

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, window.API_CONFIG.RETRY_DELAY * (i + 1)));
      }
    }
  }

  parseFormAnalysisResponse(responseText) {
    try {
      // Extract JSON from response (handle potential markdown formatting)
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      // Validate response structure
      if (!parsed.fieldMappings || !Array.isArray(parsed.fieldMappings)) {
        throw new Error('Invalid response structure');
      }

      return parsed;
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      throw new Error(window.ERROR_MESSAGES[window.ERROR_CODES.API_INVALID_RESPONSE]);
    }
  }
}
