// src/shared/models/profile-model.js
// User profile data model and validation
// Defines profile structure, validation rules, and data transformation methods

export class ProfileModel {
  constructor (data = {}) {
    this.id = data.id || this.generateId()
    this.name = data.name || ''
    this.info = data.info || ''
    this.createdAt = data.createdAt || new Date().toISOString()
    this.updatedAt = data.updatedAt || new Date().toISOString()
  }

  generateId () {
    return `profile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  validate () {
    const errors = []

    if (!this.name || this.name.trim().length === 0) {
      errors.push('Profile name is required')
    }

    if (this.name && this.name.length > 100) {
      errors.push('Profile name must be less than 100 characters')
    }

    if (!this.info || this.info.trim().length === 0) {
      errors.push('Profile information is required')
    }

    if (this.info && this.info.length > 2000) {
      errors.push('Profile information must be less than 2000 characters')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  update (data) {
    if (data.name !== undefined) this.name = data.name
    if (data.info !== undefined) this.info = data.info
    this.updatedAt = new Date().toISOString()
  }

  toJSON () {
    return {
      id: this.id,
      name: this.name,
      info: this.info,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    }
  }

  static fromJSON (data) {
    return new ProfileModel(data)
  }

  static createSample () {
    return new ProfileModel({
      name: 'Sample Profile',
      info: 'This is a sample profile for demonstration purposes.'
    })
  }
}
