// src/shared/models/form-model.js
// Form structure data model for DOM analysis and field mapping
// Defines form representation, field detection, and semantic analysis

export class FormFieldModel {
  constructor (data = {}) {
    this.selector = data.selector || ''
    this.type = data.type || 'text'
    this.label = data.label || ''
    this.placeholder = data.placeholder || ''
    this.name = data.name || ''
    this.id = data.id || ''
    this.required = data.required || false
    this.semanticLabel = data.semanticLabel || ''
    this.confidence = data.confidence || 0
    this.element = data.element || null
  }

  toJSON () {
    return {
      selector: this.selector,
      type: this.type,
      label: this.label,
      placeholder: this.placeholder,
      name: this.name,
      id: this.id,
      required: this.required,
      semanticLabel: this.semanticLabel,
      confidence: this.confidence
    }
  }
}

export class FormModel {
  constructor (data = {}) {
    this.url = data.url || window.location.href
    this.title = data.title || document.title
    this.formSelector = data.formSelector || ''
    this.fields = (data.fields || []).map(field =>
      field instanceof FormFieldModel ? field : new FormFieldModel(field)
    )
    this.analyzedAt = data.analyzedAt || new Date().toISOString()
  }

  addField (fieldData) {
    const field = fieldData instanceof FormFieldModel ? fieldData : new FormFieldModel(fieldData)
    this.fields.push(field)
  }

  getFieldBySelector (selector) {
    return this.fields.find(field => field.selector === selector)
  }

  getFieldsByType (type) {
    return this.fields.filter(field => field.type === type)
  }

  getRequiredFields () {
    return this.fields.filter(field => field.required)
  }

  toJSON () {
    return {
      url: this.url,
      title: this.title,
      formSelector: this.formSelector,
      fields: this.fields.map(field => field.toJSON()),
      analyzedAt: this.analyzedAt
    }
  }

  static fromJSON (data) {
    return new FormModel(data)
  }

  // Create simplified representation for AI analysis
  toAIPrompt () {
    const formData = {
      pageTitle: this.title,
      pageUrl: this.url,
      fields: this.fields.map(field => ({
        type: field.type,
        label: field.label,
        placeholder: field.placeholder,
        name: field.name,
        id: field.id,
        required: field.required
      }))
    }

    return JSON.stringify(formData, null, 2)
  }
}
