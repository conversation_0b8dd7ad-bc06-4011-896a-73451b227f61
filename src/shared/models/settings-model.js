// src/shared/models/settings-model.js
// Application settings data model and configuration management
// Defines settings structure, default values, and validation

export class SettingsModel {
  constructor (data = {}) {
    this.apiKey = data.apiKey || ''
    this.modelConfig = {
      temperature: data.modelConfig?.temperature ?? 0.1,
      maxOutputTokens: data.modelConfig?.maxOutputTokens ?? 1000,
      thinkingBudget: data.modelConfig?.thinkingBudget ?? 0,
      ...data.modelConfig
    }
    this.uiConfig = {
      sidebarPosition: data.uiConfig?.sidebarPosition || 'right',
      autoAnalyze: data.uiConfig?.autoAnalyze ?? true,
      showConfirmation: data.uiConfig?.showConfirmation ?? true,
      ...data.uiConfig
    }
    this.privacyConfig = {
      storeLocally: data.privacyConfig?.storeLocally ?? true,
      encryptData: data.privacyConfig?.encryptData ?? false,
      clearOnExit: data.privacyConfig?.clearOnExit ?? false,
      ...data.privacyConfig
    }
    this.updatedAt = data.updatedAt || new Date().toISOString()
  }

  validate () {
    const errors = []

    // API Key validation
    if (!this.apiKey || this.apiKey.trim().length === 0) {
      errors.push('API key is required')
    }

    // Model config validation
    if (this.modelConfig.temperature < 0 || this.modelConfig.temperature > 2) {
      errors.push('Temperature must be between 0 and 2')
    }

    if (this.modelConfig.maxOutputTokens < 1 || this.modelConfig.maxOutputTokens > 64000) {
      errors.push('Max output tokens must be between 1 and 64000')
    }

    // UI config validation
    if (!['left', 'right'].includes(this.uiConfig.sidebarPosition)) {
      errors.push('Sidebar position must be "left" or "right"')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  update (data) {
    if (data.apiKey !== undefined) this.apiKey = data.apiKey
    if (data.modelConfig) {
      this.modelConfig = { ...this.modelConfig, ...data.modelConfig }
    }
    if (data.uiConfig) {
      this.uiConfig = { ...this.uiConfig, ...data.uiConfig }
    }
    if (data.privacyConfig) {
      this.privacyConfig = { ...this.privacyConfig, ...data.privacyConfig }
    }
    this.updatedAt = new Date().toISOString()
  }

  toJSON () {
    return {
      apiKey: this.apiKey,
      modelConfig: this.modelConfig,
      uiConfig: this.uiConfig,
      privacyConfig: this.privacyConfig,
      updatedAt: this.updatedAt
    }
  }

  static fromJSON (data) {
    return new SettingsModel(data)
  }

  static getDefaults () {
    return new SettingsModel()
  }
}
