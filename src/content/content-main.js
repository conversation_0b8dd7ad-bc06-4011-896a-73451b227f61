// src/content/content-main.js
// Main content script for Smart Autofill Assistant
// Handles page analysis, form detection, and field injection

class ContentScript {
  constructor() {
    this.autofillService = null;
    this.currentFormModel = null;
    this.isAnalyzing = false;

    this.init();
  }

  init() {
    this.setupMessageListener();
    this.observePageChanges();
    console.log('Smart Autofill content script loaded');
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'ping':
          sendResponse({ success: true, status: 'ready' });
          break;

        case 'analyzePage':
          const analysisResult = await this.analyzePage();
          sendResponse(analysisResult);
          break;

        case 'performAutofill':
          const autofillResult = await this.performAutofill(
            message.profileInfo,
            message.settings
          );
          sendResponse(autofillResult);
          break;

        case 'previewAutofill':
          const previewResult = await this.previewAutofill(
            message.profileInfo,
            message.settings
          );
          sendResponse(previewResult);
          break;

        case 'highlightFields':
          this.highlightDetectedFields();
          sendResponse({ success: true });
          break;

        case 'clearHighlights':
          this.clearFieldHighlights();
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Content script error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async analyzePage() {
    if (this.isAnalyzing) {
      return { success: false, error: 'Analysis already in progress' };
    }

    this.isAnalyzing = true;

    try {
      // Find forms on the page
      const forms = window.DOMUtils.findForms();

      if (forms.length === 0) {
        return {
          success: false,
          error: 'No forms found on this page',
          formCount: 0
        };
      }

      // Analyze the most relevant form
      const targetForm = this.selectBestForm(forms);
      const formModel = await this.analyzeForm(targetForm);

      this.currentFormModel = formModel;

      return {
        success: true,
        formModel: formModel.toJSON(),
        formCount: forms.length,
        fieldCount: formModel.fields.length
      };
    } catch (error) {
      console.error('Page analysis failed:', error);
      return { success: false, error: error.message };
    } finally {
      this.isAnalyzing = false;
    }
  }

  selectBestForm(forms) {
    // Prioritize forms with more visible fields
    return forms.reduce((best, current) => {
      const bestScore = this.scoreForm(best);
      const currentScore = this.scoreForm(current);
      return currentScore > bestScore ? current : best;
    });
  }

  scoreForm(form) {
    const fields = window.DOMUtils.findFormFields(form);
    let score = fields.length;

    // Bonus for forms with common field types
    const commonTypes = ['email', 'tel', 'text', 'textarea'];
    fields.forEach(field => {
      if (commonTypes.includes(field.type)) {
        score += 2;
      }
      if (field.required) {
        score += 1;
      }
    });

    // Penalty for hidden forms
    const rect = form.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      score -= 10;
    }

    return score;
  }

  async analyzeForm(formElement) {
    const formModel = new FormModel({
      formSelector: DOMUtils.getFieldSelector(formElement)
    });

    const fields = DOMUtils.findFormFields(formElement);

    for (const fieldElement of fields) {
      const fieldModel = {
        selector: DOMUtils.getFieldSelector(fieldElement),
        type: DOMUtils.getFieldType(fieldElement),
        label: DOMUtils.getFieldLabel(fieldElement),
        placeholder: fieldElement.placeholder || '',
        name: fieldElement.name || '',
        id: fieldElement.id || '',
        required: fieldElement.required || false,
        element: fieldElement
      };

      formModel.addField(fieldModel);
    }

    return formModel;
  }

  async performAutofill(profileInfo, settings) {
    try {
      // Initialize autofill service with API key
      if (!settings.apiKey) {
        return { success: false, error: 'API key not configured' };
      }

      this.autofillService = new AutofillService(settings.apiKey);

      // Analyze page if not already done
      if (!this.currentFormModel) {
        const analysisResult = await this.analyzePage();
        if (!analysisResult.success) {
          return analysisResult;
        }
      }

      // Perform autofill
      const result = await this.autofillService.fillForm(
        this.currentFormModel,
        profileInfo
      );

      // Add visual feedback
      this.showAutofillFeedback(result);

      return {
        success: true,
        results: result,
        fieldsProcessed: this.currentFormModel.fields.length
      };
    } catch (error) {
      console.error('Autofill failed:', error);
      return { success: false, error: error.message };
    }
  }

  async previewAutofill(profileInfo, settings) {
    try {
      if (!settings.apiKey) {
        return { success: false, error: 'API key not configured' };
      }

      this.autofillService = new AutofillService(settings.apiKey);

      // Analyze page if not already done
      if (!this.currentFormModel) {
        const analysisResult = await this.analyzePage();
        if (!analysisResult.success) {
          return analysisResult;
        }
      }

      // Get preview without actually filling
      const preview = await this.autofillService.previewAutofill(
        this.currentFormModel,
        profileInfo
      );

      return preview;
    } catch (error) {
      console.error('Autofill preview failed:', error);
      return { success: false, error: error.message };
    }
  }

  showAutofillFeedback(results) {
    // Highlight filled fields
    results.filled.forEach(item => {
      const field = this.currentFormModel.getFieldBySelector(item.selector);
      if (field && field.element) {
        DOMUtils.highlightField(field.element, 3000);
      }
    });

    // Show notification
    this.showNotification(
      `Autofill complete: ${results.filled.length} fields filled`,
      'success'
    );
  }

  highlightDetectedFields() {
    if (!this.currentFormModel) return;

    this.currentFormModel.fields.forEach(field => {
      if (field.element) {
        field.element.style.outline = '2px solid #667eea';
        field.element.style.outlineOffset = '2px';
      }
    });

    // Clear highlights after 5 seconds
    setTimeout(() => {
      this.clearFieldHighlights();
    }, 5000);
  }

  clearFieldHighlights() {
    if (!this.currentFormModel) return;

    this.currentFormModel.fields.forEach(field => {
      if (field.element) {
        field.element.style.outline = '';
        field.element.style.outlineOffset = '';
      }
    });
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `autofill-notification autofill-notification-${type}`;
    notification.textContent = message;

    // Style the notification
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: type === 'success' ? '#38a169' : '#3182ce',
      color: 'white',
      padding: '12px 20px',
      borderRadius: '8px',
      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
      zIndex: '10000',
      fontSize: '14px',
      fontFamily: 'system-ui, sans-serif',
      maxWidth: '300px',
      animation: 'slideInRight 0.3s ease-out'
    });

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
      @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
      }
    `;
    document.head.appendChild(style);

    // Add to page
    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease-in';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  observePageChanges() {
    // Watch for dynamic form changes
    const observer = new MutationObserver((mutations) => {
      let shouldReanalyze = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // Check if forms were added or removed
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.tagName === 'FORM' || node.querySelector('form')) {
                shouldReanalyze = true;
              }
            }
          });
        }
      });

      if (shouldReanalyze) {
        // Clear current form model to trigger re-analysis
        this.currentFormModel = null;
        console.log('Page structure changed, form analysis reset');
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

// Initialize content script when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new ContentScript();
  });
} else {
  new ContentScript();
}
