// test/setup.js
// Jest test setup and global configuration
// Provides mocks and utilities for testing browser extension functionality

// Mock chrome extension APIs
global.chrome = {
  runtime: {
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    sendMessage: jest.fn(),
    onInstalled: {
      addListener: jest.fn()
    },
    getManifest: jest.fn(() => ({
      version: '1.0.0',
      name: 'Smart Autofill Assistant'
    }))
  },

  storage: {
    local: {
      get: jest.fn((keys) => Promise.resolve({})),
      set: jest.fn(() => Promise.resolve()),
      remove: jest.fn(() => Promise.resolve()),
      clear: jest.fn(() => Promise.resolve())
    },
    sync: {
      get: jest.fn((keys) => Promise.resolve({})),
      set: jest.fn(() => Promise.resolve()),
      remove: jest.fn(() => Promise.resolve()),
      clear: jest.fn(() => Promise.resolve())
    }
  },

  tabs: {
    query: jest.fn(() => Promise.resolve([{ id: 1, url: 'https://example.com' }])),
    sendMessage: jest.fn(() => Promise.resolve({ success: true })),
    onUpdated: {
      addListener: jest.fn()
    }
  },

  scripting: {
    executeScript: jest.fn(() => Promise.resolve([{ result: true }]))
  },

  action: {
    onClicked: {
      addListener: jest.fn()
    }
  },

  contextMenus: {
    create: jest.fn(),
    onClicked: {
      addListener: jest.fn()
    }
  },

  permissions: {
    getAll: jest.fn(() => Promise.resolve({
      permissions: ['storage', 'activeTab', 'scripting']
    }))
  }
}

// Mock fetch for API calls
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({
      candidates: [{
        content: {
          parts: [{
            text: JSON.stringify({
              fieldMappings: [],
              unmappedFields: []
            })
          }]
        }
      }]
    })
  })
)

// Mock DOM APIs
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    length: 0,
    key: jest.fn()
  },
  writable: true
})

// Mock document methods
Object.defineProperty(document, 'querySelector', {
  value: jest.fn(),
  writable: true
})

Object.defineProperty(document, 'querySelectorAll', {
  value: jest.fn(() => []),
  writable: true
})

Object.defineProperty(document, 'createElement', {
  value: jest.fn((tagName) => ({
    tagName: tagName.toUpperCase(),
    style: {},
    classList: {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn(),
      toggle: jest.fn()
    },
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    appendChild: jest.fn(),
    removeChild: jest.fn(),
    getAttribute: jest.fn(),
    setAttribute: jest.fn(),
    removeAttribute: jest.fn(),
    hasAttribute: jest.fn(),
    getBoundingClientRect: jest.fn(() => ({
      top: 0,
      left: 0,
      width: 100,
      height: 20
    })),
    closest: jest.fn(),
    querySelector: jest.fn(),
    querySelectorAll: jest.fn(() => [])
  })),
  writable: true
})

// Mock URL constructor
global.URL = jest.fn((url) => ({
  href: url,
  origin: 'https://example.com',
  pathname: '/test',
  search: '',
  hash: ''
}))

// Mock MutationObserver
global.MutationObserver = jest.fn(() => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  takeRecords: jest.fn()
}))

// Mock performance API
global.performance = {
  now: jest.fn(() => Date.now())
}

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn((callback) => {
  setTimeout(callback, 16)
  return 1
})

global.cancelAnimationFrame = jest.fn()

// Mock console methods to reduce noise in tests
const originalConsole = global.console
global.console = {
  ...originalConsole,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
}

// Test utilities
global.testUtils = {
  // Create mock form element
  createMockForm: (fields = []) => ({
    tagName: 'FORM',
    action: 'https://example.com/submit',
    method: 'POST',
    querySelector: jest.fn(),
    querySelectorAll: jest.fn(() => fields),
    addEventListener: jest.fn(),
    getBoundingClientRect: jest.fn(() => ({
      top: 0,
      left: 0,
      width: 400,
      height: 300
    }))
  }),

  // Create mock input element
  createMockInput: (type = 'text', attributes = {}) => ({
    tagName: 'INPUT',
    type,
    value: '',
    name: attributes.name || '',
    id: attributes.id || '',
    placeholder: attributes.placeholder || '',
    required: attributes.required || false,
    disabled: false,
    readOnly: false,
    style: { display: 'block', visibility: 'visible' },
    getBoundingClientRect: jest.fn(() => ({
      top: 0,
      left: 0,
      width: 200,
      height: 30
    })),
    closest: jest.fn(),
    addEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
    focus: jest.fn(),
    blur: jest.fn(),
    ...attributes
  }),

  // Wait for async operations
  waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),

  // Mock API response
  mockApiResponse: (data) => {
    global.fetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: () => Promise.resolve(data)
    })
  },

  // Mock API error
  mockApiError: (status = 500, message = 'Server Error') => {
    global.fetch.mockRejectedValueOnce(new Error(message))
  }
}

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks()

  // Reset chrome API mocks
  chrome.storage.local.get.mockClear()
  chrome.storage.local.set.mockClear()
  chrome.tabs.sendMessage.mockClear()

  // Reset fetch mock
  global.fetch.mockClear()

  // Reset localStorage mock
  localStorage.getItem.mockClear()
  localStorage.setItem.mockClear()
  localStorage.removeItem.mockClear()
  localStorage.clear.mockClear()
})

// Global test timeout
jest.setTimeout(10000)
