// test/unit/profile-service.test.js
// Unit tests for ProfileService
// Tests CRUD operations, validation, and error handling

import { ProfileService } from '../../src/shared/services/profile-service.js'
import { ProfileModel } from '../../src/shared/models/profile-model.js'

// Mock StorageUtils for testing
const mockStorage = new Map()
const StorageUtilsMock = {
  async get (key, defaultValue) {
    return mockStorage.get(key) || defaultValue
  },

  async set (key, value) {
    mockStorage.set(key, value)
  },

  async remove (key) {
    mockStorage.delete(key)
  },

  async clear () {
    mockStorage.clear()
  }
}

// Replace StorageUtils with mock
jest.mock('../../src/shared/utils/storage-utils.js', () => ({
  StorageUtils: StorageUtilsMock
}))

describe('ProfileService', () => {
  beforeEach(() => {
    mockStorage.clear()
  })

  describe('getAllProfiles', () => {
    test('should return empty array when no profiles exist', async () => {
      const profiles = await ProfileService.getAllProfiles()
      expect(profiles).toEqual([])
    })

    test('should return array of ProfileModel instances', async () => {
      const testProfiles = [
        {
          id: 'test1',
          name: 'Test Profile',
          info: 'Test information',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ]

      mockStorage.set('autofill_profiles', testProfiles)

      const profiles = await ProfileService.getAllProfiles()
      expect(profiles).toHaveLength(1)
      expect(profiles[0]).toBeInstanceOf(ProfileModel)
      expect(profiles[0].name).toBe('Test Profile')
    })
  })

  describe('getProfile', () => {
    test('should return profile by ID', async () => {
      const testProfile = {
        id: 'test1',
        name: 'Test Profile',
        info: 'Test information',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      mockStorage.set('autofill_profiles', [testProfile])

      const profile = await ProfileService.getProfile('test1')
      expect(profile).toBeInstanceOf(ProfileModel)
      expect(profile.id).toBe('test1')
      expect(profile.name).toBe('Test Profile')
    })

    test('should throw error for non-existent profile', async () => {
      await expect(ProfileService.getProfile('nonexistent'))
        .rejects.toThrow('Profile not found')
    })
  })

  describe('saveProfile', () => {
    test('should save new profile', async () => {
      const profileData = {
        name: 'New Profile',
        info: 'New profile information'
      }

      const savedProfile = await ProfileService.saveProfile(profileData)

      expect(savedProfile).toBeInstanceOf(ProfileModel)
      expect(savedProfile.name).toBe('New Profile')
      expect(savedProfile.id).toBeDefined()

      const allProfiles = await ProfileService.getAllProfiles()
      expect(allProfiles).toHaveLength(1)
    })

    test('should update existing profile', async () => {
      const existingProfile = new ProfileModel({
        name: 'Original Name',
        info: 'Original info'
      })

      await ProfileService.saveProfile(existingProfile)

      existingProfile.update({
        name: 'Updated Name',
        info: 'Updated info'
      })

      const updatedProfile = await ProfileService.saveProfile(existingProfile)

      expect(updatedProfile.name).toBe('Updated Name')
      expect(updatedProfile.info).toBe('Updated info')

      const allProfiles = await ProfileService.getAllProfiles()
      expect(allProfiles).toHaveLength(1)
    })

    test('should validate profile before saving', async () => {
      const invalidProfile = {
        name: '', // Invalid: empty name
        info: 'Some info'
      }

      await expect(ProfileService.saveProfile(invalidProfile))
        .rejects.toThrow()
    })
  })

  describe('deleteProfile', () => {
    test('should delete existing profile', async () => {
      const profile = await ProfileService.createProfile('Test Profile', 'Test info')

      const result = await ProfileService.deleteProfile(profile.id)
      expect(result).toBe(true)

      const allProfiles = await ProfileService.getAllProfiles()
      expect(allProfiles).toHaveLength(0)
    })

    test('should throw error for non-existent profile', async () => {
      await expect(ProfileService.deleteProfile('nonexistent'))
        .rejects.toThrow('Profile not found')
    })
  })

  describe('createProfile', () => {
    test('should create profile with valid data', async () => {
      const profile = await ProfileService.createProfile(
        'Test Profile',
        'Test information'
      )

      expect(profile).toBeInstanceOf(ProfileModel)
      expect(profile.name).toBe('Test Profile')
      expect(profile.info).toBe('Test information')
      expect(profile.id).toBeDefined()
    })

    test('should validate input data', async () => {
      await expect(ProfileService.createProfile('', 'Test info'))
        .rejects.toThrow()

      await expect(ProfileService.createProfile('Test', ''))
        .rejects.toThrow()
    })
  })

  describe('updateProfile', () => {
    test('should update existing profile', async () => {
      const profile = await ProfileService.createProfile('Original', 'Original info')

      const updatedProfile = await ProfileService.updateProfile(profile.id, {
        name: 'Updated',
        info: 'Updated info'
      })

      expect(updatedProfile.name).toBe('Updated')
      expect(updatedProfile.info).toBe('Updated info')
      expect(updatedProfile.updatedAt).not.toBe(profile.updatedAt)
    })
  })

  describe('searchProfiles', () => {
    beforeEach(async () => {
      await ProfileService.createProfile('John Doe', 'Software engineer at Tech Corp')
      await ProfileService.createProfile('Jane Smith', 'Designer at Creative Agency')
      await ProfileService.createProfile('Bob Johnson', 'Manager at Tech Corp')
    })

    test('should search by name', async () => {
      const results = await ProfileService.searchProfiles('john')
      expect(results).toHaveLength(2) // John Doe and Bob Johnson
    })

    test('should search by info', async () => {
      const results = await ProfileService.searchProfiles('tech corp')
      expect(results).toHaveLength(2) // John and Bob work at Tech Corp
    })

    test('should return empty array for no matches', async () => {
      const results = await ProfileService.searchProfiles('nonexistent')
      expect(results).toHaveLength(0)
    })
  })

  describe('exportProfiles', () => {
    test('should export profiles in correct format', async () => {
      await ProfileService.createProfile('Test 1', 'Info 1')
      await ProfileService.createProfile('Test 2', 'Info 2')

      const exportData = await ProfileService.exportProfiles()

      expect(exportData).toHaveProperty('version')
      expect(exportData).toHaveProperty('exportDate')
      expect(exportData).toHaveProperty('profiles')
      expect(exportData.profiles).toHaveLength(2)
    })
  })

  describe('importProfiles', () => {
    test('should import valid profiles', async () => {
      const importData = {
        version: '1.0.0',
        exportDate: '2024-01-01T00:00:00Z',
        profiles: [
          {
            name: 'Imported 1',
            info: 'Imported info 1'
          },
          {
            name: 'Imported 2',
            info: 'Imported info 2'
          }
        ]
      }

      const importedCount = await ProfileService.importProfiles(importData)
      expect(importedCount).toBe(2)

      const allProfiles = await ProfileService.getAllProfiles()
      expect(allProfiles).toHaveLength(2)
    })

    test('should skip invalid profiles during import', async () => {
      const importData = {
        profiles: [
          {
            name: 'Valid Profile',
            info: 'Valid info'
          },
          {
            name: '', // Invalid
            info: 'Invalid profile'
          }
        ]
      }

      const importedCount = await ProfileService.importProfiles(importData)
      expect(importedCount).toBe(1)
    })
  })
})
